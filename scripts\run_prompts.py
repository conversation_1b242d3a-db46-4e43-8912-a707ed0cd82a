import argparse
from pathlib import Path
import pandas as pd
from time import perf_counter
from ollama_utils import generate  # your helper to call Ollama

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("pos_prompt_csv", nargs="?", help="(legacy) prompt CSV path")
    parser.add_argument("pos_model", nargs="?", help="(legacy) model tag")
    parser.add_argument("--prompt_csv")
    parser.add_argument("--model")
    parser.add_argument("--out", default=None)
    parser.add_argument("--samples", type=int, default=1)
    args = parser.parse_args()

    # ---- Harmonize legacy vs. flag style -----------------------------
    prompt_csv = args.prompt_csv or args.pos_prompt_csv
    model_tag  = args.model      or args.pos_model
    if prompt_csv is None or model_tag is None:
        parser.error("Need prompt CSV and model tag")

    prompt_path = Path(prompt_csv)
    if not prompt_path.exists():
        raise FileNotFoundError(prompt_path)

    out_path = Path(args.out) if args.out else (
        Path("data/outputs") / f"{prompt_path.stem}_{model_tag.replace(':','-')}.csv"
    )
    out_path.parent.mkdir(parents=True, exist_ok=True)

    # ------------------------------------------------------------------
    df = pd.read_csv(prompt_path)
    rows = []
    for idx, row in df.iterrows():
        prompt = row["prompt"]
        for s in range(args.samples):
            try:
                print(f"[{idx}-{s}] Generating with {model_tag}... ", end="", flush=True)
                t0 = perf_counter()
                resp = generate(prompt, model_tag)
                latency = perf_counter() - t0
                print(f"done ({latency:.2f}s)")
            except Exception as e:
                print(f"error: {e}")
                resp = "ERROR"
                latency = -1

            rows.append({
                "idx": idx,
                "sample": s,
                "prompt": prompt,
                "response": resp,
                "latency_s": latency,
            })

    pd.DataFrame(rows).to_csv(out_path, index=False)
    print(f"\n✅ Finished. Saved {len(rows)} results to:")
    print(f"   {out_path.resolve()}")

if __name__ == "__main__":
    main()
