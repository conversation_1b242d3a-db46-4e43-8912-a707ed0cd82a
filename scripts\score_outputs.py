#!/usr/bin/env python
"""
score_outputs.py
────────────────
Score all model–method output CSVs against their gold dev-set answers.

Usage (PowerShell, Bash, …):
    python scripts/score_outputs.py \
        --outputs_dir data/outputs \
        --datasets_dir data \
        --out_dir      data/scores
"""

from __future__ import annotations
import argparse, os, re, sys, unicodedata
from pathlib import Path
from typing import Callable, Dict, List

import pandas as pd
from tqdm import tqdm


# ───────────────────────────────────────────────────────── scorer helpers ──

def _norm_text(text: str) -> str:
    """Lower-case, strip, remove articles/punctuation/extra spaces."""
    def _white_space_fix(s: str) -> str:
        return " ".join(s.split())

    def _remove_punctuation(s: str) -> str:
        return "".join(ch for ch in s if unicodedata.category(ch)[0] != "P")

    text = text.lower()
    text = _remove_punctuation(text)
    text = _white_space_fix(text)
    return text


# GSM-8K ────────────────────────────────────────────────────────────────────
def _extract_last_number(text: str) -> str | None:
    """Return the last numeric token in a string (int or float)."""
    import re
    nums = re.findall(r"[-+]?\d*\.?\d+", text.replace(",", ""))
    return nums[-1].lstrip("0") if nums else None

def score_gsm8k(row: pd.Series) -> bool:
    pred = _extract_last_number(str(row["response"]))
    gold = _extract_last_number(str(row["answer"]))
    return pred is not None and gold is not None and pred == gold


# ARC-Challenge ────────────────────────────────────────────────────────────
def score_arc(row: pd.Series) -> bool:
    m = re.match(r"([A-E])", str(row["response"]).strip().upper())
    pred_letter = m.group(1) if m else "?"
    return pred_letter == str(row["answerKey"]).strip().upper()


# TriviaQA (short answer) ──────────────────────────────────────────────────
def score_triviaqa(row: pd.Series) -> bool:
    gold_answers = [a.strip() for a in str(row["answers"]).split("|||") if a.strip()]
    pred = _norm_text(str(row["response"]))
    return any(_norm_text(g) == pred for g in gold_answers)


# Alpaca-Eval (exact instruction following; simple equality on `output`)
def score_alpaca(row: pd.Series) -> bool:
    return _norm_text(str(row["response"])) == _norm_text(str(row.get("output", "")))


# Map
scorer_map: Dict[str, Callable[[pd.Series], bool]] = {
    "gsm8k": score_gsm8k,
    "arc":   score_arc,
    "triviaqa": score_triviaqa,
    "alpaca_eval": score_alpaca,
}

# ───────────────────────────────────────────────────────────────────────────


def _parse_file_info(fname: str):
    """
    Expect   dataset_method_model.csv
    Example  gsm8k_cot_mistral-7b.csv
    Returns  ("gsm8k","cot","mistral-7b")
    """
    stem = Path(fname).stem
    parts = stem.split("_", 2)
    if len(parts) != 3:
        raise ValueError(f"Filename not in expected pattern: {fname}")
    return parts[0], parts[1], parts[2]


def main() -> None:
    ap = argparse.ArgumentParser()
    ap.add_argument("--outputs_dir",  required=True)
    ap.add_argument("--datasets_dir", required=True)
    ap.add_argument("--out_dir",      required=True)
    args = ap.parse_args()

    outputs_dir  = Path(args.outputs_dir)
    datasets_dir = Path(args.datasets_dir)
    out_dir      = Path(args.out_dir)
    out_dir.mkdir(parents=True, exist_ok=True)

    output_files = sorted(f.name for f in outputs_dir.glob("*.csv"))
    all_scores: List[dict] = []

    for file in tqdm(output_files, desc="Scoring outputs"):
        try:
            df_out = pd.read_csv(outputs_dir / file)
            dataset, method, model = _parse_file_info(file)

            # -- reference
            ref_csv = datasets_dir / dataset / "dev.csv"
            if not ref_csv.exists():
                print(f"  ➜ reference {ref_csv} missing; skipping")
                continue
            df_gold = pd.read_csv(ref_csv)

            # merge on idx (created during prompt generation / inference)
            if "idx" not in df_out.columns:
                raise KeyError("column 'idx' not found in output CSV")

            df = df_out.merge(df_gold, on="idx", suffixes=("", "_gold"))

            # score
            if dataset not in scorer_map:
                raise KeyError(f"No scorer for dataset '{dataset}'")
            df["is_correct"] = df.apply(scorer_map[dataset], axis=1)

            acc     = df["is_correct"].mean()
            n       = len(df)
            latency = df["latency_s"].mean()

            # save per-file scored copy
            df.to_csv(out_dir / file, index=False)

            print(f"✔ {file:40} → acc={acc:5.2%}   n={n:<4d}   latency≈ {latency:5.2f}s")

            # summary row
            all_scores.append({
                "dataset": dataset,
                "method":  method,
                "model":   model,
                "n":       n,
                "accuracy": round(acc, 4),
                "latency_s": round(latency, 3),
            })

        except Exception as e:
            print(f"✖ Error scoring {file}: {e}")

    # write summary
    if all_scores:
        df_sum = pd.DataFrame(all_scores)
        df_sum.to_csv(out_dir / "summary.csv", index=False)
        print(f"\n📊 Summary saved → {out_dir / 'summary.csv'}")
    else:
        print("\n⚠ No results were scored. Check for earlier errors.")


if __name__ == "__main__":
    main()
